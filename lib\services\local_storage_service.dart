import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:flutter_image_compress/flutter_image_compress.dart';

/// Service for managing local image storage and caching
class LocalStorageService {
  static final LocalStorageService _instance = LocalStorageService._internal();
  factory LocalStorageService() => _instance;
  LocalStorageService._internal();

  static const String _chatImagesFolder = 'chat_images';
  static const String _thumbnailsFolder = 'thumbnails';
  static const String _tempFolder = 'temp';

  /// Initialize local storage directories
  static Future<void> initialize() async {
    try {
      await _createDirectories();
      await _cleanupOldFiles();
    } catch (e) {
      // print('Error initializing local storage: $e');
    }
  }

  /// Create necessary directories
  static Future<void> _createDirectories() async {
    final appDir = await getApplicationDocumentsDirectory();
    
    final chatImagesDir = Directory(path.join(appDir.path, _chatImagesFolder));
    final thumbnailsDir = Directory(path.join(appDir.path, _thumbnailsFolder));
    final tempDir = Directory(path.join(appDir.path, _tempFolder));

    await chatImagesDir.create(recursive: true);
    await thumbnailsDir.create(recursive: true);
    await tempDir.create(recursive: true);
  }

  /// Store image locally and return the local path
  static Future<String?> storeImageLocally(
    File sourceFile, {
    String? customFileName,
    bool compress = true,
  }) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final chatImagesDir = Directory(path.join(appDir.path, _chatImagesFolder));
      
      // Generate unique filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(sourceFile.path);
      final fileName = customFileName ?? 'img_${timestamp}$extension';
      final localPath = path.join(chatImagesDir.path, fileName);

      File localFile;
      
      if (compress && _isImageFile(extension)) {
        // Compress image before storing
        final compressedFile = await FlutterImageCompress.compressAndGetFile(
          sourceFile.path,
          localPath,
          quality: 85,
          minWidth: 1024,
          minHeight: 1024,
          format: CompressFormat.jpeg,
          keepExif: false,
        );
        
        if (compressedFile != null) {
          localFile = File(compressedFile.path);
        } else {
          // Fallback to copying original file
          localFile = await sourceFile.copy(localPath);
        }
      } else {
        // Copy file without compression
        localFile = await sourceFile.copy(localPath);
      }

      return localFile.path;
    } catch (e) {
      // print('Error storing image locally: $e');
      return null;
    }
  }

  /// Generate and store thumbnail locally
  static Future<String?> generateAndStoreThumbnail(
    String imagePath, {
    int width = 200,
    int height = 200,
  }) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final thumbnailsDir = Directory(path.join(appDir.path, _thumbnailsFolder));
      
      // Generate unique thumbnail filename
      final originalFileName = path.basenameWithoutExtension(imagePath);
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final thumbnailPath = path.join(
        thumbnailsDir.path, 
        '${originalFileName}_thumb_${timestamp}.jpg'
      );

      final thumbnailBytes = await FlutterImageCompress.compressWithFile(
        imagePath,
        minWidth: width,
        minHeight: height,
        quality: 60,
        format: CompressFormat.jpeg,
      );

      if (thumbnailBytes != null) {
        final thumbnailFile = File(thumbnailPath);
        await thumbnailFile.writeAsBytes(thumbnailBytes);
        return thumbnailPath;
      }

      return null;
    } catch (e) {
      // print('Error generating thumbnail: $e');
      return null;
    }
  }

  /// Get local file if it exists
  static Future<File?> getLocalFile(String localPath) async {
    try {
      final file = File(localPath);
      if (await file.exists()) {
        return file;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Check if local file exists
  static Future<bool> localFileExists(String localPath) async {
    try {
      final file = File(localPath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// Delete local file
  static Future<bool> deleteLocalFile(String localPath) async {
    try {
      final file = File(localPath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Get cache directory for temporary files
  static Future<Directory> getTempDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final tempDir = Directory(path.join(appDir.path, _tempFolder));
    await tempDir.create(recursive: true);
    return tempDir;
  }

  /// Clean up old files (older than 30 days)
  static Future<void> _cleanupOldFiles() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
      
      // Clean up chat images
      final chatImagesDir = Directory(path.join(appDir.path, _chatImagesFolder));
      if (await chatImagesDir.exists()) {
        await _cleanupDirectory(chatImagesDir, cutoffDate);
      }
      
      // Clean up thumbnails
      final thumbnailsDir = Directory(path.join(appDir.path, _thumbnailsFolder));
      if (await thumbnailsDir.exists()) {
        await _cleanupDirectory(thumbnailsDir, cutoffDate);
      }
      
      // Clean up temp files (older than 1 day)
      final tempDir = Directory(path.join(appDir.path, _tempFolder));
      if (await tempDir.exists()) {
        final tempCutoff = DateTime.now().subtract(const Duration(days: 1));
        await _cleanupDirectory(tempDir, tempCutoff);
      }
    } catch (e) {
      // print('Error during cleanup: $e');
    }
  }

  /// Clean up files in directory older than cutoff date
  static Future<void> _cleanupDirectory(Directory dir, DateTime cutoffDate) async {
    try {
      final files = dir.listSync();
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            try {
              await file.delete();
            } catch (e) {
              // print('Error deleting file ${file.path}: $e');
            }
          }
        }
      }
    } catch (e) {
      // print('Error cleaning directory ${dir.path}: $e');
    }
  }

  /// Get total size of cached files
  static Future<int> getCacheSize() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      int totalSize = 0;
      
      final chatImagesDir = Directory(path.join(appDir.path, _chatImagesFolder));
      if (await chatImagesDir.exists()) {
        totalSize += await _getDirectorySize(chatImagesDir);
      }
      
      final thumbnailsDir = Directory(path.join(appDir.path, _thumbnailsFolder));
      if (await thumbnailsDir.exists()) {
        totalSize += await _getDirectorySize(thumbnailsDir);
      }
      
      return totalSize;
    } catch (e) {
      return 0;
    }
  }

  /// Get size of directory
  static Future<int> _getDirectorySize(Directory dir) async {
    int size = 0;
    try {
      final files = dir.listSync(recursive: true);
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          size += stat.size;
        }
      }
    } catch (e) {
      // print('Error calculating directory size: $e');
    }
    return size;
  }

  /// Clear all cached files
  static Future<void> clearCache() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      
      final chatImagesDir = Directory(path.join(appDir.path, _chatImagesFolder));
      if (await chatImagesDir.exists()) {
        await chatImagesDir.delete(recursive: true);
      }
      
      final thumbnailsDir = Directory(path.join(appDir.path, _thumbnailsFolder));
      if (await thumbnailsDir.exists()) {
        await thumbnailsDir.delete(recursive: true);
      }
      
      final tempDir = Directory(path.join(appDir.path, _tempFolder));
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
      
      // Recreate directories
      await _createDirectories();
    } catch (e) {
      // print('Error clearing cache: $e');
    }
  }

  /// Check if file is an image
  static bool _isImageFile(String extension) {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    return imageExtensions.contains(extension.toLowerCase());
  }

  /// Format file size for display
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
