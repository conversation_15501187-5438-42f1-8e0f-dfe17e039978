import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/services/local_storage_service.dart';
import 'package:mr_garments_mobile/utils/enhanced_image_cache.dart';

/// Service for optimizing chat performance with lazy loading and caching
class ChatPerformanceService {
  static final ChatPerformanceService _instance =
      ChatPerformanceService._internal();
  factory ChatPerformanceService() => _instance;
  ChatPerformanceService._internal();

  // Performance settings
  static const int messagesPerPage = 50;
  static const int maxCachedMessages = 500;
  static const int preloadImageCount = 10;
  static const Duration debounceDelay = Duration(milliseconds: 300);

  // Cache for messages
  final Map<String, List<Message>> _messageCache = {};
  final Map<String, int> _lastLoadedIndex = {};
  final Map<String, bool> _isLoading = {};

  // Debounce timers
  final Map<String, Timer> _debounceTimers = {};

  /// Initialize performance service for a chat
  Future<void> initializeChat(String chatId) async {
    _messageCache[chatId] = [];
    _lastLoadedIndex[chatId] = 0;
    _isLoading[chatId] = false;

    // Initialize local storage
    await LocalStorageService.initialize();

    // Preload recent images
    await _preloadRecentImages(chatId);
  }

  /// Load messages with pagination
  Future<List<Message>> loadMessages(
    String chatId, {
    int? startIndex,
    int? limit,
    bool refresh = false,
  }) async {
    if (_isLoading[chatId] == true && !refresh) {
      return _messageCache[chatId] ?? [];
    }

    _isLoading[chatId] = true;

    try {
      final loadLimit = limit ?? messagesPerPage;
      final loadStart = startIndex ?? _lastLoadedIndex[chatId] ?? 0;

      // Simulate loading messages (replace with actual service call)
      final newMessages = await _loadMessagesFromService(
        chatId,
        startIndex: loadStart,
        limit: loadLimit,
      );

      if (refresh) {
        _messageCache[chatId] = newMessages;
        _lastLoadedIndex[chatId] = newMessages.length;
      } else {
        final existingMessages = _messageCache[chatId] ?? [];
        _messageCache[chatId] = [...existingMessages, ...newMessages];
        _lastLoadedIndex[chatId] =
            (_lastLoadedIndex[chatId] ?? 0) + newMessages.length;
      }

      // Limit cache size
      _limitCacheSize(chatId);

      // Preload images for new messages
      await _preloadImagesForMessages(newMessages);

      return _messageCache[chatId] ?? [];
    } finally {
      _isLoading[chatId] = false;
    }
  }

  /// Load more messages (for infinite scroll)
  Future<List<Message>> loadMoreMessages(String chatId) async {
    return await loadMessages(chatId);
  }

  /// Add pending message to cache immediately
  void addPendingMessage(String chatId, Message message) {
    final messages = _messageCache[chatId] ?? [];
    _messageCache[chatId] = [message, ...messages];
    _limitCacheSize(chatId);
  }

  /// Update message in cache
  void updateMessage(String chatId, Message updatedMessage) {
    final messages = _messageCache[chatId];
    if (messages == null) return;

    final index = messages.indexWhere((msg) => msg.id == updatedMessage.id);
    if (index != -1) {
      messages[index] = updatedMessage;
    }
  }

  /// Remove message from cache
  void removeMessage(String chatId, String messageId) {
    final messages = _messageCache[chatId];
    if (messages == null) return;

    messages.removeWhere((msg) => msg.id == messageId);
  }

  /// Get cached messages
  List<Message> getCachedMessages(String chatId) {
    return _messageCache[chatId] ?? [];
  }

  /// Check if more messages can be loaded
  bool canLoadMore(String chatId) {
    return _isLoading[chatId] != true;
  }

  /// Debounced message loading
  void loadMessagesDebounced(String chatId, VoidCallback onLoad) {
    _debounceTimers[chatId]?.cancel();
    _debounceTimers[chatId] = Timer(debounceDelay, onLoad);
  }

  /// Preload images for better performance
  Future<void> _preloadImagesForMessages(List<Message> messages) async {
    final imageMessages =
        messages
            .where((msg) => msg.type == MessageType.image && msg.hasRemoteUrl)
            .take(preloadImageCount)
            .toList();

    if (imageMessages.isNotEmpty) {
      final imageUrls = imageMessages.map((msg) => msg.mediaUrl!).toList();

      await EnhancedImageCache.preloadImages(imageUrls);
    }
  }

  /// Preload recent images for a chat
  Future<void> _preloadRecentImages(String chatId) async {
    final messages = _messageCache[chatId] ?? [];
    await _preloadImagesForMessages(messages);
  }

  /// Limit cache size to prevent memory issues
  void _limitCacheSize(String chatId) {
    final messages = _messageCache[chatId];
    if (messages == null) return;

    if (messages.length > maxCachedMessages) {
      // Keep only the most recent messages
      _messageCache[chatId] = messages.take(maxCachedMessages).toList();
    }
  }

  /// Simulate loading messages from service (replace with actual implementation)
  Future<List<Message>> _loadMessagesFromService(
    String chatId, {
    required int startIndex,
    required int limit,
  }) async {
    // This should be replaced with actual ChatService call
    await Future.delayed(
      const Duration(milliseconds: 500),
    ); // Simulate network delay
    return []; // Return actual messages from service
  }

  /// Clear cache for a chat
  void clearCache(String chatId) {
    _messageCache.remove(chatId);
    _lastLoadedIndex.remove(chatId);
    _isLoading.remove(chatId);
    _debounceTimers[chatId]?.cancel();
    _debounceTimers.remove(chatId);
  }

  /// Clear all caches
  void clearAllCaches() {
    _messageCache.clear();
    _lastLoadedIndex.clear();
    _isLoading.clear();

    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    _debounceTimers.clear();
  }

  /// Get memory usage statistics
  Map<String, dynamic> getMemoryStats() {
    int totalMessages = 0;
    int totalImageMessages = 0;

    for (final messages in _messageCache.values) {
      totalMessages += messages.length;
      totalImageMessages +=
          messages.where((msg) => msg.type == MessageType.image).length;
    }

    return {
      'totalChats': _messageCache.length,
      'totalMessages': totalMessages,
      'totalImageMessages': totalImageMessages,
      'averageMessagesPerChat':
          totalMessages / (_messageCache.isNotEmpty ? _messageCache.length : 1),
    };
  }

  /// Cleanup old data
  Future<void> cleanup() async {
    // Clear old local files
    await LocalStorageService.initialize();

    // Clear image cache
    try {
      await EnhancedImageCache.clearAllCache();
    } catch (e) {
      // Ignore if clearAllCache method fails
    }

    // Clear message caches for inactive chats
    // This could be enhanced to only clear chats not accessed recently
    clearAllCaches();
  }

  /// Dispose resources
  void dispose() {
    clearAllCaches();
  }
}

/// Mixin for widgets that need performance optimization
mixin ChatPerformanceMixin {
  final ChatPerformanceService _performanceService = ChatPerformanceService();

  /// Initialize performance for a chat
  Future<void> initializeChatPerformance(String chatId) async {
    await _performanceService.initializeChat(chatId);
  }

  /// Load messages with performance optimization
  Future<List<Message>> loadMessagesOptimized(
    String chatId, {
    bool refresh = false,
  }) async {
    return await _performanceService.loadMessages(chatId, refresh: refresh);
  }

  /// Add pending message optimistically
  void addPendingMessageOptimized(String chatId, Message message) {
    _performanceService.addPendingMessage(chatId, message);
  }

  /// Update message with optimization
  void updateMessageOptimized(String chatId, Message message) {
    _performanceService.updateMessage(chatId, message);
  }

  /// Get cached messages
  List<Message> getCachedMessagesOptimized(String chatId) {
    return _performanceService.getCachedMessages(chatId);
  }

  /// Cleanup performance resources
  void cleanupChatPerformance(String chatId) {
    _performanceService.clearCache(chatId);
  }
}

/// Performance monitoring utility
class ChatPerformanceMonitor {
  static final Stopwatch _stopwatch = Stopwatch();
  static final Map<String, Duration> _operationTimes = {};

  /// Start timing an operation
  static void startTiming(String operation) {
    _stopwatch.reset();
    _stopwatch.start();
  }

  /// End timing an operation
  static void endTiming(String operation) {
    _stopwatch.stop();
    _operationTimes[operation] = _stopwatch.elapsed;

    if (kDebugMode) {
      print('Performance: $operation took ${_stopwatch.elapsedMilliseconds}ms');
    }
  }

  /// Get timing for an operation
  static Duration? getOperationTime(String operation) {
    return _operationTimes[operation];
  }

  /// Get all operation times
  static Map<String, Duration> getAllOperationTimes() {
    return Map.from(_operationTimes);
  }

  /// Clear timing data
  static void clearTimings() {
    _operationTimes.clear();
  }
}
