import 'package:cloud_firestore/cloud_firestore.dart';

enum MessageType {
  text,
  image,
  file,
  audio,
  video,
  location,
  contact,
  order,
  catalog,
}

enum MessageStatus {
  pending, // Message created locally, not yet uploaded
  uploading, // Currently uploading to server
  sending, // Upload complete, sending to Firestore
  sent, // Successfully sent to Firestore
  delivered, // Delivered to recipient
  read, // Read by recipient
  failed, // Upload or send failed
}

class Message {
  final String id;
  final String senderId;
  final String senderName;
  final String? senderProfileUrl;
  final MessageType type;
  final String? text;
  final String? mediaUrl; // Remote URL from Firebase Storage
  final String? localPath; // Local file path for optimistic UI
  final String? fileName;
  final int? fileSize;
  final String? thumbnailUrl;
  final String? localThumbnailPath; // Local thumbnail path
  final MessageStatus status;
  final DateTime timestamp;
  final String? replyToMessageId;
  final String? replyToText;
  final String? replyToSenderName;
  final bool isForwarded;
  final List<String> readBy;
  final List<String> deletedBy; // Track which users have deleted this message
  final Map<String, dynamic>? metadata; // For order, catalog, location data
  final double? uploadProgress; // Upload progress (0.0 to 1.0)
  final String? uploadError; // Error message if upload failed

  Message({
    required this.id,
    required this.senderId,
    required this.senderName,
    this.senderProfileUrl,
    required this.type,
    this.text,
    this.mediaUrl,
    this.localPath,
    this.fileName,
    this.fileSize,
    this.thumbnailUrl,
    this.localThumbnailPath,
    this.status = MessageStatus.pending,
    required this.timestamp,
    this.replyToMessageId,
    this.replyToText,
    this.replyToSenderName,
    this.isForwarded = false,
    this.readBy = const [],
    this.deletedBy = const [],
    this.metadata,
    this.uploadProgress,
    this.uploadError,
  });

  // Convert Message to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'senderId': senderId,
      'senderName': senderName,
      'senderProfileUrl': senderProfileUrl,
      'type': type.name,
      'text': text,
      'mediaUrl': mediaUrl,
      // Note: localPath and localThumbnailPath are not stored in Firestore
      'fileName': fileName,
      'fileSize': fileSize,
      'thumbnailUrl': thumbnailUrl,
      'status': status.name,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'replyToMessageId': replyToMessageId,
      'replyToText': replyToText,
      'replyToSenderName': replyToSenderName,
      'isForwarded': isForwarded,
      'readBy': readBy,
      'deletedBy': deletedBy,
      'metadata': metadata,
      // uploadProgress and uploadError are also not stored in Firestore
    };
  }

  // Create Message from Firestore document
  factory Message.fromMap(Map<String, dynamic> map) {
    return Message(
      id: map['id'] ?? '',
      senderId: map['senderId'] ?? '',
      senderName: map['senderName'] ?? '',
      senderProfileUrl: map['senderProfileUrl'],
      type: MessageType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => MessageType.text,
      ),
      text: map['text'],
      mediaUrl: map['mediaUrl'],
      // localPath is not stored in Firestore, will be null for remote messages
      localPath: null,
      fileName: map['fileName'],
      fileSize: map['fileSize'],
      thumbnailUrl: map['thumbnailUrl'],
      // localThumbnailPath is not stored in Firestore
      localThumbnailPath: null,
      status: MessageStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => MessageStatus.sent,
      ),
      timestamp: _parseDateTime(map['timestamp']),
      replyToMessageId: map['replyToMessageId'],
      replyToText: map['replyToText'],
      replyToSenderName: map['replyToSenderName'],
      isForwarded: map['isForwarded'] ?? false,
      readBy: List<String>.from(map['readBy'] ?? []),
      deletedBy: List<String>.from(map['deletedBy'] ?? []),
      metadata: map['metadata'],
    );
  }

  // Helper method to parse DateTime from various formats
  static DateTime _parseDateTime(dynamic value) {
    if (value == null) {
      return DateTime.now();
    }

    if (value is int) {
      return DateTime.fromMillisecondsSinceEpoch(value);
    }

    if (value is Timestamp) {
      return value.toDate();
    }

    if (value is DateTime) {
      return value;
    }

    // Fallback to current time if we can't parse
    return DateTime.now();
  }

  // Create Message from Firestore DocumentSnapshot
  factory Message.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Message.fromMap(data);
  }

  // Copy with method for updating message data
  Message copyWith({
    String? id,
    String? senderId,
    String? senderName,
    String? senderProfileUrl,
    MessageType? type,
    String? text,
    String? mediaUrl,
    String? localPath,
    String? fileName,
    int? fileSize,
    String? thumbnailUrl,
    String? localThumbnailPath,
    MessageStatus? status,
    DateTime? timestamp,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
    bool? isForwarded,
    List<String>? readBy,
    List<String>? deletedBy,
    Map<String, dynamic>? metadata,
    double? uploadProgress,
    String? uploadError,
  }) {
    return Message(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderProfileUrl: senderProfileUrl ?? this.senderProfileUrl,
      type: type ?? this.type,
      text: text ?? this.text,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      localPath: localPath ?? this.localPath,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      localThumbnailPath: localThumbnailPath ?? this.localThumbnailPath,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      replyToText: replyToText ?? this.replyToText,
      replyToSenderName: replyToSenderName ?? this.replyToSenderName,
      isForwarded: isForwarded ?? this.isForwarded,
      readBy: readBy ?? this.readBy,
      deletedBy: deletedBy ?? this.deletedBy,
      metadata: metadata ?? this.metadata,
      uploadProgress: uploadProgress ?? this.uploadProgress,
      uploadError: uploadError ?? this.uploadError,
    );
  }

  // Check if message is sent by current user
  bool isSentByMe(String currentUserId) {
    return senderId == currentUserId;
  }

  // Check if message is read by specific user
  bool isReadBy(String userId) {
    return readBy.contains(userId);
  }

  // Check if message is deleted by specific user
  bool isDeletedBy(String userId) {
    return deletedBy.contains(userId);
  }

  // Check if message has local file (for optimistic UI)
  bool get hasLocalFile => localPath != null && localPath!.isNotEmpty;

  // Check if message has remote URL (uploaded to server)
  bool get hasRemoteUrl => mediaUrl != null && mediaUrl!.isNotEmpty;

  // Get the appropriate image path for display (local first, then remote)
  String? get displayImagePath {
    if (hasLocalFile) return localPath;
    if (hasRemoteUrl) return mediaUrl;
    return null;
  }

  // Check if message is still uploading
  bool get isUploading =>
      status == MessageStatus.uploading || status == MessageStatus.pending;

  // Check if message upload failed
  bool get hasUploadFailed => status == MessageStatus.failed;

  // Check if message is ready for display
  bool get isReadyForDisplay => hasLocalFile || hasRemoteUrl;

  @override
  String toString() {
    return 'Message(id: $id, senderId: $senderId, type: $type, text: $text, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Message && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
