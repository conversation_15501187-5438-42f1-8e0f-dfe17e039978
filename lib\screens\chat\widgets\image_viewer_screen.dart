import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:mr_garments_mobile/screens/chat/widgets/image_forward_dialog.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/utils/enhanced_image_cache.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class ImageViewerScreen extends StatefulWidget {
  final List<String> imageUrls;
  final List<Message>? imageMessages; // Optional message objects for forwarding
  final int initialIndex;
  final String senderName;
  final String timestamp;
  final String? chatId; // Optional chat ID for forwarding
  final Function(List<int>)?
  onImagesDeleted; // Callback for when images are deleted

  const ImageViewerScreen({
    super.key,
    required this.imageUrls,
    this.imageMessages,
    this.initialIndex = 0,
    required this.senderName,
    required this.timestamp,
    this.chatId,
    this.onImagesDeleted,
  });

  @override
  State<ImageViewerScreen> createState() => _ImageViewerScreenState();
}

class _ImageViewerScreenState extends State<ImageViewerScreen> {
  late PageController _pageController;
  late int _currentIndex;
  bool _isGridView = false;
  bool _isPreloading = false;
  int _preloadedCount = 0;
  bool _isSelectionMode = false;
  Set<int> _selectedIndices = {};

  // Local copies of the image data that can be modified
  late List<String> _imageUrls;
  late List<Message>? _imageMessages;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);

    // Initialize local copies of the image data
    _imageUrls = List<String>.from(widget.imageUrls);
    _imageMessages =
        widget.imageMessages != null
            ? List<Message>.from(widget.imageMessages!)
            : null;

    _preloadImages();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// Preload all images for smooth scrolling experience
  void _preloadImages() async {
    if (_imageUrls.isEmpty) return;

    setState(() {
      _isPreloading = true;
      _preloadedCount = 0;
    });

    // Preload images with progress tracking
    await EnhancedImageCache.preloadImages(
      _imageUrls,
      onProgress: (loaded, total) {
        if (mounted) {
          setState(() {
            _preloadedCount = loaded;
          });
        }
      },
      onComplete: () {
        if (mounted) {
          setState(() {
            _isPreloading = false;
          });
        }
      },
    );
  }

  void _toggleViewMode() {
    if (_isGridView) {
      // Switching from grid to fullscreen - recreate PageController with current index
      _pageController.dispose();
      _pageController = PageController(initialPage: _currentIndex);
      debugPrint(
        'Switching to fullscreen, recreated PageController with page: $_currentIndex',
      );
    }

    setState(() {
      _isGridView = !_isGridView;
      if (!_isGridView) {
        _exitSelectionMode();
      }
    });
  }

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedIndices.clear();
      }
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedIndices.clear();
    });
  }

  void _toggleImageSelection(int index) {
    setState(() {
      if (_selectedIndices.contains(index)) {
        _selectedIndices.remove(index);
        if (_selectedIndices.isEmpty) {
          _isSelectionMode = false;
        }
      } else {
        _selectedIndices.add(index);
        _isSelectionMode = true;
      }
    });
  }

  void _selectAllImages() {
    setState(() {
      _selectedIndices = Set.from(
        List.generate(widget.imageUrls.length, (index) => index),
      );
      _isSelectionMode = true;
    });
  }

  void _deselectAllImages() {
    setState(() {
      _selectedIndices.clear();
      _isSelectionMode = false;
    });
  }

  void _onPageChanged(int index) {
    debugPrint('Page changed to index: $index (previous: $_currentIndex)');
    if (_currentIndex != index) {
      setState(() {
        _currentIndex = index;
      });
      debugPrint('Updated _currentIndex to: $_currentIndex');
    }

    // Preload adjacent images for smoother experience
    _preloadAdjacentImages(index);
  }

  void _preloadAdjacentImages(int currentIndex) {
    final imagesToPreload = <String>[];

    // Preload next 3 images
    for (int i = 1; i <= 3; i++) {
      final nextIndex = currentIndex + i;
      if (nextIndex < _imageUrls.length) {
        imagesToPreload.add(_imageUrls[nextIndex]);
      }
    }

    // Preload previous 3 images
    for (int i = 1; i <= 3; i++) {
      final prevIndex = currentIndex - i;
      if (prevIndex >= 0) {
        imagesToPreload.add(_imageUrls[prevIndex]);
      }
    }

    // Preload in background
    if (imagesToPreload.isNotEmpty) {
      EnhancedImageCache.preloadImages(imagesToPreload);
    }
  }

  void _openFullscreenImage(int index) {
    debugPrint('Opening fullscreen image at index: $index');

    // First update the current index
    _currentIndex = index;

    // Recreate the page controller with the correct initial page
    _pageController.dispose();
    _pageController = PageController(initialPage: index);

    // Then update the UI state
    setState(() {
      _isGridView = false;
    });

    debugPrint('Set up new PageController with initial page: $index');
  }

  void _showForwardDialog() async {
    if (_imageMessages != null && widget.chatId != null) {
      List<String> urlsToForward;
      List<Message> messagesToForward;

      if (_isSelectionMode && _selectedIndices.isNotEmpty) {
        // Forward only selected images
        urlsToForward =
            _selectedIndices.map((index) => _imageUrls[index]).toList();
        messagesToForward =
            _selectedIndices.map((index) => _imageMessages![index]).toList();
      } else {
        // Forward all images
        urlsToForward = _imageUrls;
        messagesToForward = _imageMessages!;
      }

      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => ImageForwardScreen(
                imageUrls: urlsToForward,
                imageMessages: messagesToForward,
                fromChatId: widget.chatId!,
              ),
        ),
      );

      // If forwarding was successful and we're in selection mode, exit it
      if (result == true && _isSelectionMode) {
        _exitSelectionMode();
      }
    }
  }

  Future<void> _deleteSelectedImages() async {
    if (_imageMessages == null || widget.chatId == null) return;

    List<int> indicesToDelete;
    List<Message> messagesToDelete;

    if (_isSelectionMode && _selectedIndices.isNotEmpty) {
      indicesToDelete = _selectedIndices.toList()..sort();
      messagesToDelete =
          indicesToDelete.map((index) => _imageMessages![index]).toList();
    } else {
      // Delete current image if no selection
      indicesToDelete = [_currentIndex];
      messagesToDelete = [_imageMessages![_currentIndex]];
    }

    // Show confirmation dialog
    final confirmed = await _showDeleteConfirmation(indicesToDelete.length);
    if (!confirmed) return;

    try {
      // Show loading
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => const Center(
                child: CircularProgressIndicator(color: Color(0xFF005368)),
              ),
        );
      }

      // Delete messages from backend
      for (final message in messagesToDelete) {
        await ChatService.deleteMessage(widget.chatId!, message.id);
      }

      // Update local state immediately - remove in reverse order to maintain indices
      final sortedIndices =
          indicesToDelete.toList()..sort((a, b) => b.compareTo(a));
      for (int index in sortedIndices) {
        if (index < _imageUrls.length) {
          _imageUrls.removeAt(index);
        }
        if (_imageMessages != null && index < _imageMessages!.length) {
          _imageMessages!.removeAt(index);
        }
      }

      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Notify parent about deleted images
      widget.onImagesDeleted?.call(indicesToDelete);

      // Handle navigation after deletion
      if (_imageUrls.isEmpty) {
        // No images left, go back
        if (mounted) Navigator.pop(context);
        return;
      }

      // Adjust current index if necessary
      if (_currentIndex >= _imageUrls.length) {
        _currentIndex = _imageUrls.length - 1;
      }

      // Recreate page controller with adjusted index
      _pageController.dispose();
      _pageController = PageController(initialPage: _currentIndex);

      // Update UI state
      if (mounted) {
        setState(() {
          if (_isSelectionMode) {
            _selectedIndices.clear();
            _isSelectionMode = false;
          }
        });

        // Show success message
        AppSnackbar.showSuccess(
          context,
          '${indicesToDelete.length} image${indicesToDelete.length > 1 ? 's' : ''} deleted successfully',
        );
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) Navigator.pop(context);

      if (mounted) {
        AppSnackbar.showError(
          context,
          'Failed to delete images: ${e.toString()}',
        );
      }
    }
  }

  Future<bool> _showDeleteConfirmation(int count) async {
    return await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                title: Text(
                  'Delete Image${count > 1 ? 's' : ''}',
                  style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                ),
                content: Text(
                  'Are you sure you want to delete $count image${count > 1 ? 's' : ''}?',
                  style: GoogleFonts.poppins(),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: Text(
                      'Cancel',
                      style: GoogleFonts.poppins(color: Colors.grey[600]),
                    ),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context, true),
                    child: Text(
                      'Delete',
                      style: GoogleFonts.poppins(color: Colors.red),
                    ),
                  ),
                ],
              ),
        ) ??
        false;
  }

  @override
  Widget build(BuildContext context) {
    if (_isGridView) {
      return _buildGridView();
    } else {
      return _buildFullscreenView();
    }
  }

  Widget _buildFullscreenView() {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          if (_isSelectionMode && _selectedIndices.isNotEmpty) ...[
            // Selection mode actions
            if (widget.imageMessages != null && widget.chatId != null)
              IconButton(
                icon: const Icon(LucideIcons.trash2, color: Colors.white),
                onPressed: _deleteSelectedImages,
              ),
            if (widget.imageMessages != null && widget.chatId != null)
              IconButton(
                icon: const Icon(LucideIcons.forward, color: Colors.white),
                onPressed: _showForwardDialog,
              ),
            IconButton(
              icon: const Icon(LucideIcons.x, color: Colors.white),
              onPressed: _exitSelectionMode,
            ),
          ] else ...[
            // Normal mode actions
            if (widget.imageMessages != null && widget.chatId != null)
              IconButton(
                icon: const Icon(LucideIcons.forward, color: Colors.white),
                onPressed: _showForwardDialog,
              ),
            IconButton(
              icon: const Icon(LucideIcons.grid, color: Colors.white),
              onPressed: _toggleViewMode,
            ),
          ],
        ],
      ),
      body: Stack(
        children: [
          PhotoViewGallery.builder(
            key: ValueKey('gallery_${_pageController.hashCode}'),
            scrollPhysics: const BouncingScrollPhysics(),
            pageController: _pageController,
            builder: (context, index) {
              debugPrint('Building PhotoView for index: $index');
              return PhotoViewGalleryPageOptions(
                imageProvider: EnhancedImageCache.getCachedImageProvider(
                  _imageUrls[index],
                ),
                initialScale: PhotoViewComputedScale.contained,
                minScale: PhotoViewComputedScale.contained,
                maxScale: PhotoViewComputedScale.covered * 2,
                heroAttributes: PhotoViewHeroAttributes(
                  tag: 'fullscreen_${_imageUrls[index]}_$index',
                ),
              );
            },
            itemCount: _imageUrls.length,
            loadingBuilder:
                (context, event) => const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                ),
            onPageChanged: (index) {
              debugPrint(
                'PhotoViewGallery onPageChanged called with index: $index',
              );
              _onPageChanged(index);
            },
          ),

          // Preloading indicator
          if (_isPreloading)
            Positioned(
              top: 100,
              left: 0,
              right: 0,
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Loading images... $_preloadedCount/${widget.imageUrls.length}',
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // Bottom info bar
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.7),
                    Colors.transparent,
                  ],
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.senderName,
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_currentIndex + 1}/${widget.imageUrls.length} • ${widget.timestamp}',
                    style: GoogleFonts.poppins(
                      color: Colors.white.withValues(alpha: 0.7),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGridView() {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          _isSelectionMode
              ? '${_selectedIndices.length} selected'
              : 'All Photos',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          if (_isSelectionMode) ...[
            // Selection mode actions
            if (_selectedIndices.length < widget.imageUrls.length)
              IconButton(
                icon: const Icon(LucideIcons.checkSquare, color: Colors.white),
                onPressed: _selectAllImages,
                tooltip: 'Select All',
              ),
            if (_selectedIndices.isNotEmpty &&
                widget.imageMessages != null &&
                widget.chatId != null)
              IconButton(
                icon: const Icon(LucideIcons.trash2, color: Colors.white),
                onPressed: _deleteSelectedImages,
                tooltip: 'Delete',
              ),
            if (_selectedIndices.isNotEmpty &&
                widget.imageMessages != null &&
                widget.chatId != null)
              IconButton(
                icon: const Icon(LucideIcons.forward, color: Colors.white),
                onPressed: _showForwardDialog,
                tooltip: 'Forward',
              ),
            IconButton(
              icon: const Icon(LucideIcons.x, color: Colors.white),
              onPressed: _exitSelectionMode,
              tooltip: 'Cancel',
            ),
          ] else ...[
            // Normal mode actions
            if (_isPreloading)
              Padding(
                padding: const EdgeInsets.only(right: 16),
                child: Center(
                  child: Text(
                    '$_preloadedCount/${_imageUrls.length}',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
          ],
        ],
      ),
      body: GridView.builder(
        padding: const EdgeInsets.all(1),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 1,
          mainAxisSpacing: 1,
        ),
        itemCount: _imageUrls.length,
        itemBuilder: (context, index) {
          final isSelected = _selectedIndices.contains(index);
          return GestureDetector(
            onTap: () {
              if (_isSelectionMode) {
                _toggleImageSelection(index);
              } else {
                _openFullscreenImage(index);
              }
            },
            onLongPress: () {
              if (!_isSelectionMode) {
                _toggleImageSelection(index);
              }
            },
            child: Stack(
              children: [
                Hero(
                  tag: 'grid_${_imageUrls[index]}',
                  child: Container(
                    decoration: BoxDecoration(
                      border:
                          isSelected
                              ? Border.all(
                                color: const Color(0xFF005368),
                                width: 3,
                              )
                              : null,
                    ),
                    child: EnhancedImageCache.buildGridImage(
                      _imageUrls[index],
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                if (isSelected)
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: const BoxDecoration(
                        color: Color(0xFF005368),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        LucideIcons.check,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                if (_isSelectionMode && !isSelected)
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.3),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }
}
