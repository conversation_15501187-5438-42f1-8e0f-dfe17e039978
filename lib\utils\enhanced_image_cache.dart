import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:lucide_icons/lucide_icons.dart';

/// Enhanced image cache manager with WhatsApp-like caching behavior
class EnhancedImageCache {
  static final EnhancedImageCache _instance = EnhancedImageCache._internal();
  factory EnhancedImageCache() => _instance;
  EnhancedImageCache._internal();

  // Custom cache manager with longer cache duration
  static final CacheManager _cacheManager = CacheManager(
    Config(
      'enhanced_image_cache',
      stalePeriod: const Duration(days: 30), // Keep images for 30 days
      maxNrOfCacheObjects: 1000, // Allow up to 1000 cached images
      repo: JsonCacheInfoRepository(databaseName: 'enhanced_image_cache'),
      fileService: HttpFileService(),
    ),
  );

  // In-memory cache for frequently accessed images
  static final Map<String, ImageProvider> _memoryCache = {};
  static final Map<String, bool> _preloadingImages = {};
  static final Map<String, List<VoidCallback>> _preloadCallbacks = {};

  /// Clean malformed image URLs
  static String cleanImageUrl(String url) {
    if (url.startsWith(
      'https://mrgarment.braincavesoft.com/storage/https://',
    )) {
      return url.replaceFirst(
        'https://mrgarment.braincavesoft.com/storage/',
        '',
      );
    }
    return url;
  }

  /// Get cached image provider with memory caching
  static ImageProvider getCachedImageProvider(String imageUrl) {
    final cleanUrl = cleanImageUrl(imageUrl);

    // Check memory cache first
    if (_memoryCache.containsKey(cleanUrl)) {
      return _memoryCache[cleanUrl]!;
    }

    // Create cached network image provider
    final provider = CachedNetworkImageProvider(
      cleanUrl,
      cacheManager: _cacheManager,
    );

    // Store in memory cache
    _memoryCache[cleanUrl] = provider;

    return provider;
  }

  /// Preload images in background with callback support
  static Future<void> preloadImages(
    List<String> imageUrls, {
    VoidCallback? onComplete,
    Function(int loaded, int total)? onProgress,
  }) async {
    if (imageUrls.isEmpty) {
      onComplete?.call();
      return;
    }

    int loadedCount = 0;
    final total = imageUrls.length;

    for (int i = 0; i < imageUrls.length; i++) {
      final cleanUrl = cleanImageUrl(imageUrls[i]);

      // Skip if already preloading
      if (_preloadingImages[cleanUrl] == true) {
        if (onComplete != null) {
          _preloadCallbacks.putIfAbsent(cleanUrl, () => []).add(onComplete);
        }
        continue;
      }

      _preloadingImages[cleanUrl] = true;

      try {
        // Check if already cached
        final fileInfo = await _cacheManager.getFileFromCache(cleanUrl);

        if (fileInfo == null) {
          // Download and cache the image
          await _cacheManager.getSingleFile(cleanUrl);
        }

        // Preload into memory cache
        final provider = CachedNetworkImageProvider(
          cleanUrl,
          cacheManager: _cacheManager,
        );
        _memoryCache[cleanUrl] = provider;

        loadedCount++;
        onProgress?.call(loadedCount, total);
      } catch (e) {
        // Continue with other images even if one fails
        print('Failed to preload image: $cleanUrl, Error: $e');
      } finally {
        _preloadingImages[cleanUrl] = false;

        // Execute callbacks
        final callbacks = _preloadCallbacks.remove(cleanUrl);
        if (callbacks != null) {
          for (final callback in callbacks) {
            callback();
          }
        }
      }
    }

    onComplete?.call();
  }

  /// Preload single image
  static Future<bool> preloadImage(String imageUrl) async {
    final cleanUrl = cleanImageUrl(imageUrl);

    try {
      // Check if already cached
      final fileInfo = await _cacheManager.getFileFromCache(cleanUrl);

      if (fileInfo == null) {
        await _cacheManager.getSingleFile(cleanUrl);
      }

      // Add to memory cache
      final provider = CachedNetworkImageProvider(
        cleanUrl,
        cacheManager: _cacheManager,
      );
      _memoryCache[cleanUrl] = provider;

      return true;
    } catch (e) {
      print('Failed to preload image: $cleanUrl, Error: $e');
      return false;
    }
  }

  /// Check if image is cached (both disk and memory)
  static Future<bool> isImageCached(String imageUrl) async {
    final cleanUrl = cleanImageUrl(imageUrl);

    // Check memory cache first
    if (_memoryCache.containsKey(cleanUrl)) {
      return true;
    }

    // Check disk cache
    try {
      final fileInfo = await _cacheManager.getFileFromCache(cleanUrl);
      return fileInfo != null;
    } catch (e) {
      return false;
    }
  }

  /// Get cache size and info
  static Future<Map<String, dynamic>> getCacheInfo() async {
    try {
      final cacheObjects = await _cacheManager.getFileFromCache('');
      return {
        'memoryCache': _memoryCache.length,
        'diskCache': 'Available',
        'preloading': _preloadingImages.values.where((v) => v).length,
      };
    } catch (e) {
      return {
        'memoryCache': _memoryCache.length,
        'diskCache': 'Error',
        'preloading': _preloadingImages.values.where((v) => v).length,
      };
    }
  }

  /// Clear memory cache (keep disk cache)
  static void clearMemoryCache() {
    _memoryCache.clear();
  }

  /// Clear all caches
  static Future<void> clearAllCache() async {
    _memoryCache.clear();
    _preloadingImages.clear();
    _preloadCallbacks.clear();
    await _cacheManager.emptyCache();
  }

  /// Build optimized cached image widget
  static Widget buildCachedImage(
    String imageUrl, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    Widget? placeholder,
    Widget? errorWidget,
    bool enableMemoryCache = true,
  }) {
    final cleanUrl = cleanImageUrl(imageUrl);

    return CachedNetworkImage(
      imageUrl: cleanUrl,
      cacheManager: _cacheManager,
      width: width,
      height: height,
      fit: fit,
      memCacheWidth: enableMemoryCache ? width?.toInt() : null,
      memCacheHeight: enableMemoryCache ? height?.toInt() : null,
      placeholder:
          (context, url) =>
              placeholder ??
              _buildDefaultPlaceholder(width, height, borderRadius),
      errorWidget:
          (context, url, error) =>
              errorWidget ??
              _buildDefaultErrorWidget(width, height, borderRadius),
      fadeInDuration: const Duration(milliseconds: 200),
      fadeOutDuration: const Duration(milliseconds: 100),
    );
  }

  /// Build optimized image for chat messages
  static Widget buildChatImage(
    String imageUrl, {
    required double width,
    required double height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    VoidCallback? onTap,
  }) {
    final imageWidget = buildCachedImage(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      borderRadius: borderRadius,
      enableMemoryCache: true,
    );

    if (borderRadius != null) {
      final clippedWidget = ClipRRect(
        borderRadius: borderRadius,
        child: imageWidget,
      );

      return onTap != null
          ? GestureDetector(onTap: onTap, child: clippedWidget)
          : clippedWidget;
    }

    return onTap != null
        ? GestureDetector(onTap: onTap, child: imageWidget)
        : imageWidget;
  }

  /// Build optimized image for grid views (like image viewer)
  static Widget buildGridImage(
    String imageUrl, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    VoidCallback? onTap,
  }) {
    final imageWidget = buildCachedImage(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      enableMemoryCache: true,
      placeholder: Container(
        width: width,
        height: height,
        color: Colors.grey[900],
        child: const Center(
          // child: CircularProgressIndicator(
          //   color: Colors.white,
          //   strokeWidth: 2,
          // ),
        ),
      ),
      errorWidget: Container(
        width: width,
        height: height,
        color: Colors.grey[900],
        child: const Center(
          child: Icon(LucideIcons.image, color: Colors.white54, size: 32),
        ),
      ),
    );

    return onTap != null
        ? GestureDetector(onTap: onTap, child: imageWidget)
        : imageWidget;
  }

  static Widget _buildDefaultPlaceholder(
    double? width,
    double? height,
    BorderRadius? borderRadius,
  ) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: borderRadius,
      ),
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF005368)),
          strokeWidth: 2,
        ),
      ),
    );
  }

  static Widget _buildDefaultErrorWidget(
    double? width,
    double? height,
    BorderRadius? borderRadius,
  ) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: borderRadius,
      ),
      child: const Center(
        child: Icon(LucideIcons.image, color: Colors.grey, size: 24),
      ),
    );
  }
}

/// Enhanced image cache widget with automatic preloading
class CachedImageWidget extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final VoidCallback? onTap;
  final Widget? placeholder;
  final Widget? errorWidget;
  final bool preloadOnInit;

  const CachedImageWidget({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.onTap,
    this.placeholder,
    this.errorWidget,
    this.preloadOnInit = false,
  });

  @override
  State<CachedImageWidget> createState() => _CachedImageWidgetState();
}

class _CachedImageWidgetState extends State<CachedImageWidget> {
  @override
  void initState() {
    super.initState();
    if (widget.preloadOnInit) {
      EnhancedImageCache.preloadImage(widget.imageUrl);
    }
  }

  @override
  Widget build(BuildContext context) {
    return EnhancedImageCache.buildCachedImage(
      widget.imageUrl,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      borderRadius: widget.borderRadius,
      placeholder: widget.placeholder,
      errorWidget: widget.errorWidget,
    );
  }
}
