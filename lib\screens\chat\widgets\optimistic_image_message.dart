import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/utils/enhanced_image_cache.dart';

/// Widget for displaying image messages with optimistic UI updates
class OptimisticImageMessage extends StatelessWidget {
  final Message message;
  final bool isMe;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onRetry;

  const OptimisticImageMessage({
    super.key,
    required this.message,
    required this.isMe,
    this.onTap,
    this.onLongPress,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: Container(
        constraints: const BoxConstraints(
          minWidth: 200,
          maxWidth: 280,
          minHeight: 150,
          maxHeight: 350,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              // Image display
              _buildImageDisplay(),

              // Status overlay
              _buildStatusOverlay(),

              // Progress indicator
              if (message.isUploading && message.uploadProgress != null)
                _buildProgressIndicator(),

              // Retry button for failed uploads
              if (message.hasUploadFailed) _buildRetryButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageDisplay() {
    final imagePath = message.displayImagePath;

    if (imagePath == null) {
      return _buildPlaceholder();
    }

    // Display local image if available
    if (message.hasLocalFile) {
      return Image.file(
        File(imagePath),
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
      );
    }

    // Display remote image with caching
    if (message.hasRemoteUrl) {
      return EnhancedImageCache.buildCachedImage(
        imagePath,
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        placeholder: _buildPlaceholder(),
        errorWidget: _buildErrorWidget(),
      );
    }

    return _buildPlaceholder();
  }

  Widget _buildStatusOverlay() {
    Color overlayColor;
    IconData statusIcon;

    switch (message.status) {
      case MessageStatus.pending:
        overlayColor = Colors.black.withValues(alpha: 0.3);
        statusIcon = LucideIcons.clock;
        break;
      case MessageStatus.uploading:
        overlayColor = Colors.black.withValues(alpha: 0.2);
        statusIcon = LucideIcons.upload;
        break;
      case MessageStatus.sending:
        overlayColor = Colors.black.withValues(alpha: 0.1);
        statusIcon = LucideIcons.clock;
        break;
      case MessageStatus.sent:
        return const SizedBox.shrink(); // No overlay for sent messages
      case MessageStatus.delivered:
        return const SizedBox.shrink();
      case MessageStatus.read:
        return const SizedBox.shrink();
      case MessageStatus.failed:
        overlayColor = Colors.red.withValues(alpha: 0.3);
        statusIcon = LucideIcons.alertCircle;
        break;
    }

    return Positioned.fill(
      child: Container(
        color: overlayColor,
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(statusIcon, color: Colors.white, size: 20),
          ),
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final progress = message.uploadProgress ?? 0.0;

    return Positioned(
      bottom: 8,
      left: 8,
      right: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Icon(LucideIcons.upload, color: Colors.white, size: 14),
                const SizedBox(width: 6),
                Text(
                  'Uploading ${(progress * 100).toInt()}%',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.white.withValues(alpha: 0.3),
              valueColor: const AlwaysStoppedAnimation<Color>(
                Color(0xFF005368),
              ),
              minHeight: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRetryButton() {
    return Positioned(
      bottom: 8,
      right: 8,
      child: GestureDetector(
        onTap: onRetry,
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.red,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(LucideIcons.refreshCw, color: Colors.white, size: 16),
              const SizedBox(width: 4),
              Text(
                'Retry',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[300],
      child: const Center(
        child: Icon(LucideIcons.image, color: Colors.grey, size: 40),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[300],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(LucideIcons.imageOff, color: Colors.grey, size: 40),
          const SizedBox(height: 8),
          Text(
            'Failed to load image',
            style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Widget for displaying message status icons
class MessageStatusIcon extends StatelessWidget {
  final MessageStatus status;
  final bool isMe;
  final double size;

  const MessageStatusIcon({
    super.key,
    required this.status,
    required this.isMe,
    this.size = 16,
  });

  @override
  Widget build(BuildContext context) {
    if (!isMe) return const SizedBox.shrink();

    IconData icon;
    Color color;

    switch (status) {
      case MessageStatus.pending:
        icon = LucideIcons.clock;
        color = Colors.grey;
        break;
      case MessageStatus.uploading:
        icon = LucideIcons.upload;
        color = Colors.orange;
        break;
      case MessageStatus.sending:
        icon = LucideIcons.clock;
        color = Colors.grey;
        break;
      case MessageStatus.sent:
        icon = LucideIcons.check;
        color = Colors.grey;
        break;
      case MessageStatus.delivered:
        icon = LucideIcons.checkCheck;
        color = Colors.grey;
        break;
      case MessageStatus.read:
        icon = LucideIcons.checkCheck;
        color = const Color(0xFF005368);
        break;
      case MessageStatus.failed:
        icon = LucideIcons.alertCircle;
        color = Colors.red;
        break;
    }

    return Icon(icon, size: size, color: color);
  }
}

/// Enhanced grouped image message widget with optimistic UI support
class OptimisticGroupedImageMessage extends StatelessWidget {
  final List<Message> imageMessages;
  final String senderName;
  final String timestamp;
  final bool isMe;
  final String chatId;
  final VoidCallback? onLongPress;
  final Function(Message)? onRetry;

  const OptimisticGroupedImageMessage({
    super.key,
    required this.imageMessages,
    required this.senderName,
    required this.timestamp,
    required this.isMe,
    required this.chatId,
    this.onLongPress,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    if (imageMessages.isEmpty) return const SizedBox.shrink();

    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      margin: EdgeInsets.only(
        left: isMe ? 50 : 16,
        right: isMe ? 16 : 50,
        bottom: 8,
      ),
      child: Column(
        crossAxisAlignment:
            isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          // Sender name for group chats
          if (!isMe)
            Padding(
              padding: const EdgeInsets.only(left: 12, bottom: 4),
              child: Text(
                senderName,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF005368),
                ),
              ),
            ),

          // Image grid
          _buildImageGrid(screenWidth),

          // Timestamp and status
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  timestamp,
                  style: GoogleFonts.poppins(
                    fontSize: 11,
                    color: Colors.grey[600],
                  ),
                ),
                if (isMe) ...[
                  const SizedBox(width: 4),
                  MessageStatusIcon(
                    status: _getOverallStatus(),
                    isMe: isMe,
                    size: 14,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageGrid(double screenWidth) {
    if (imageMessages.length == 1) {
      return _buildSingleImage(screenWidth);
    } else {
      return _buildMultipleImages(screenWidth);
    }
  }

  Widget _buildSingleImage(double screenWidth) {
    final message = imageMessages.first;
    final maxWidth = screenWidth * 0.6;
    final maxHeight = 300.0;

    return GestureDetector(
      onLongPress: onLongPress,
      child: Container(
        constraints: BoxConstraints(
          minWidth: 200,
          maxWidth: maxWidth,
          minHeight: 150,
          maxHeight: maxHeight,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: OptimisticImageMessage(
            message: message,
            isMe: isMe,
            onRetry: () => onRetry?.call(message),
          ),
        ),
      ),
    );
  }

  Widget _buildMultipleImages(double screenWidth) {
    final containerWidth = screenWidth * 0.55;
    final containerHeight = 220.0;

    return GestureDetector(
      onLongPress: onLongPress,
      child: Container(
        width: containerWidth,
        height: containerHeight,
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
        child: _buildImageLayout(),
      ),
    );
  }

  Widget _buildImageLayout() {
    if (imageMessages.length == 2) {
      return _buildTwoImageLayout();
    } else if (imageMessages.length == 3) {
      return _buildThreeImageLayout();
    } else {
      return _buildFourPlusImageLayout();
    }
  }

  Widget _buildTwoImageLayout() {
    return Row(
      children: [
        Expanded(
          child: OptimisticImageMessage(
            message: imageMessages[0],
            isMe: isMe,
            onRetry: () => onRetry?.call(imageMessages[0]),
          ),
        ),
        const SizedBox(width: 2),
        Expanded(
          child: OptimisticImageMessage(
            message: imageMessages[1],
            isMe: isMe,
            onRetry: () => onRetry?.call(imageMessages[1]),
          ),
        ),
      ],
    );
  }

  Widget _buildThreeImageLayout() {
    return Row(
      children: [
        Expanded(
          child: OptimisticImageMessage(
            message: imageMessages[0],
            isMe: isMe,
            onRetry: () => onRetry?.call(imageMessages[0]),
          ),
        ),
        const SizedBox(width: 2),
        Expanded(
          child: Column(
            children: [
              Expanded(
                child: OptimisticImageMessage(
                  message: imageMessages[1],
                  isMe: isMe,
                  onRetry: () => onRetry?.call(imageMessages[1]),
                ),
              ),
              const SizedBox(height: 2),
              Expanded(
                child: OptimisticImageMessage(
                  message: imageMessages[2],
                  isMe: isMe,
                  onRetry: () => onRetry?.call(imageMessages[2]),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFourPlusImageLayout() {
    final remainingCount = imageMessages.length - 3;

    return Row(
      children: [
        Expanded(
          child: OptimisticImageMessage(
            message: imageMessages[0],
            isMe: isMe,
            onRetry: () => onRetry?.call(imageMessages[0]),
          ),
        ),
        const SizedBox(width: 2),
        Expanded(
          child: Column(
            children: [
              Expanded(
                child: OptimisticImageMessage(
                  message: imageMessages[1],
                  isMe: isMe,
                  onRetry: () => onRetry?.call(imageMessages[1]),
                ),
              ),
              const SizedBox(height: 2),
              Expanded(
                child: Stack(
                  children: [
                    OptimisticImageMessage(
                      message: imageMessages[2],
                      isMe: isMe,
                      onRetry: () => onRetry?.call(imageMessages[2]),
                    ),
                    if (remainingCount > 0)
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.5),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: Text(
                              '+$remainingCount',
                              style: GoogleFonts.poppins(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Get overall status for the group (worst status wins)
  MessageStatus _getOverallStatus() {
    if (imageMessages.any((msg) => msg.status == MessageStatus.failed)) {
      return MessageStatus.failed;
    }
    if (imageMessages.any((msg) => msg.status == MessageStatus.pending)) {
      return MessageStatus.pending;
    }
    if (imageMessages.any((msg) => msg.status == MessageStatus.uploading)) {
      return MessageStatus.uploading;
    }
    if (imageMessages.any((msg) => msg.status == MessageStatus.sending)) {
      return MessageStatus.sending;
    }
    if (imageMessages.every((msg) => msg.status == MessageStatus.read)) {
      return MessageStatus.read;
    }
    if (imageMessages.every(
      (msg) =>
          msg.status == MessageStatus.delivered ||
          msg.status == MessageStatus.read,
    )) {
      return MessageStatus.delivered;
    }
    return MessageStatus.sent;
  }
}
