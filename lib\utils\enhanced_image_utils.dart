import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:mr_garments_mobile/utils/enhanced_image_cache.dart';
import 'package:mr_garments_mobile/models/message.dart';

/// Enhanced image utilities that can handle both local files and network URLs
/// for WhatsApp-like immediate image display
class EnhancedImageUtils {
  /// Build image widget that can handle both local files and network URLs
  static Widget buildChatImage(
    String imagePath, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    bool isLocalFile = false,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    // Check if this is a local file path
    final isLocal = isLocalFile || _isLocalFilePath(imagePath);

    Widget imageWidget;

    if (isLocal) {
      // Handle local file
      imageWidget = _buildLocalImage(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        errorWidget: errorWidget,
      );
    } else {
      // Handle network URL
      imageWidget = _buildNetworkImage(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        placeholder: placeholder,
        errorWidget: errorWidget,
      );
    }

    // Apply border radius if specified
    if (borderRadius != null) {
      imageWidget = ClipRRect(borderRadius: borderRadius, child: imageWidget);
    }

    return imageWidget;
  }

  /// Build local file image
  static Widget _buildLocalImage(
    String filePath, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? errorWidget,
  }) {
    return Image.file(
      File(filePath),
      width: width,
      height: height,
      fit: fit,
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ?? _buildDefaultErrorWidget(width, height);
      },
    );
  }

  /// Build network image with caching
  static Widget _buildNetworkImage(
    String imageUrl, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    return EnhancedImageCache.buildChatImage(
      imageUrl,
      width: width ?? 200,
      height: height ?? 200,
      fit: fit,
      borderRadius: BorderRadius.zero,
    );
  }

  /// Check if a path is a local file path
  static bool _isLocalFilePath(String path) {
    // Check for common local file path patterns
    return path.startsWith('/') || // Unix-style absolute path
        path.contains('\\') || // Windows-style path
        path.startsWith('file://') || // File URI
        !path.startsWith('http'); // Not a URL
  }

  /// Build default error widget
  static Widget _buildDefaultErrorWidget(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[300],
      child: const Icon(Icons.broken_image, color: Colors.grey, size: 32),
    );
  }

  /// Build default placeholder widget
  static Widget buildDefaultPlaceholder(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[200],
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
        ),
      ),
    );
  }

  /// Build image with loading overlay for pending messages
  static Widget buildPendingImage(
    String imagePath, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    bool showLoadingOverlay = true,
    double loadingOpacity = 0.7,
  }) {
    Widget imageWidget = buildChatImage(
      imagePath,
      width: width,
      height: height,
      fit: fit,
      borderRadius: borderRadius,
      isLocalFile: true,
    );

    if (showLoadingOverlay) {
      imageWidget = Stack(
        children: [
          imageWidget,
          Positioned.fill(
            child: Container(
              color: Colors.black.withValues(alpha: 1 - loadingOpacity),
              child: const Center(
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
          ),
        ],
      );
    }

    return imageWidget;
  }

  /// Build image with status indicator
  static Widget buildImageWithStatus(
    String imagePath, {
    required MessageStatus status,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    bool isLocalFile = false,
  }) {
    Widget imageWidget = buildChatImage(
      imagePath,
      width: width,
      height: height,
      fit: fit,
      borderRadius: borderRadius,
      isLocalFile: isLocalFile,
    );

    // Add status overlay based on message status
    Widget? statusOverlay;

    switch (status) {
      case MessageStatus.sending:
        statusOverlay = Container(
          color: Colors.black.withValues(alpha: 0.3),
          child: const Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
        );
        break;
      case MessageStatus.failed:
        statusOverlay = Container(
          color: Colors.black.withValues(alpha: 0.5),
          child: const Center(
            child: Icon(Icons.error_outline, color: Colors.red, size: 32),
          ),
        );
        break;
      case MessageStatus.sent:
      case MessageStatus.delivered:
      case MessageStatus.read:
        // No overlay for successful states
        break;
    }

    if (statusOverlay != null) {
      imageWidget = Stack(
        children: [imageWidget, Positioned.fill(child: statusOverlay)],
      );
    }

    return imageWidget;
  }
}
