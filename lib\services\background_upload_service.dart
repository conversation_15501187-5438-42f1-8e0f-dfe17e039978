import 'dart:io';
import 'dart:async';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/services/session_service.dart';

/// Upload status for tracking individual uploads
enum UploadStatus {
  queued,
  uploading,
  completed,
  failed,
  cancelled,
}

/// Upload task data
class UploadTask {
  final String id;
  final String messageId;
  final String chatId;
  final File localFile;
  final String fileName;
  final DateTime createdAt;
  UploadStatus status;
  double progress;
  String? remoteUrl;
  String? error;
  int retryCount;

  UploadTask({
    required this.id,
    required this.messageId,
    required this.chatId,
    required this.localFile,
    required this.fileName,
    required this.createdAt,
    this.status = UploadStatus.queued,
    this.progress = 0.0,
    this.remoteUrl,
    this.error,
    this.retryCount = 0,
  });

  UploadTask copyWith({
    UploadStatus? status,
    double? progress,
    String? remoteUrl,
    String? error,
    int? retryCount,
  }) {
    return UploadTask(
      id: id,
      messageId: messageId,
      chatId: chatId,
      localFile: localFile,
      fileName: fileName,
      createdAt: createdAt,
      status: status ?? this.status,
      progress: progress ?? this.progress,
      remoteUrl: remoteUrl ?? this.remoteUrl,
      error: error ?? this.error,
      retryCount: retryCount ?? this.retryCount,
    );
  }
}

/// Background upload service for handling image uploads
class BackgroundUploadService {
  static final BackgroundUploadService _instance = BackgroundUploadService._internal();
  factory BackgroundUploadService() => _instance;
  BackgroundUploadService._internal();

  final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  final Map<String, UploadTask> _uploadTasks = {};
  final Map<String, StreamSubscription> _uploadSubscriptions = {};
  final StreamController<UploadTask> _uploadStatusController = StreamController.broadcast();

  static const int maxRetries = 3;
  static const int maxConcurrentUploads = 3;
  int _activeUploads = 0;

  /// Stream of upload status updates
  Stream<UploadTask> get uploadStatusStream => _uploadStatusController.stream;

  /// Queue an image for upload
  Future<String> queueUpload({
    required String messageId,
    required String chatId,
    required File localFile,
    String? customFileName,
  }) async {
    final uploadId = '${messageId}_${DateTime.now().millisecondsSinceEpoch}';
    final fileName = customFileName ?? 'img_${DateTime.now().millisecondsSinceEpoch}.jpg';

    final uploadTask = UploadTask(
      id: uploadId,
      messageId: messageId,
      chatId: chatId,
      localFile: localFile,
      fileName: fileName,
      createdAt: DateTime.now(),
    );

    _uploadTasks[uploadId] = uploadTask;
    _uploadStatusController.add(uploadTask);

    // Start upload if we have capacity
    _processUploadQueue();

    return uploadId;
  }

  /// Process the upload queue
  void _processUploadQueue() {
    if (_activeUploads >= maxConcurrentUploads) return;

    final queuedTasks = _uploadTasks.values
        .where((task) => task.status == UploadStatus.queued)
        .toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));

    for (final task in queuedTasks) {
      if (_activeUploads >= maxConcurrentUploads) break;
      _startUpload(task);
    }
  }

  /// Start uploading a specific task
  Future<void> _startUpload(UploadTask task) async {
    if (_activeUploads >= maxConcurrentUploads) return;

    _activeUploads++;
    task.status = UploadStatus.uploading;
    _uploadStatusController.add(task);

    try {
      await _ensureFirebaseAuth();
      
      if (_auth.currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Create Firebase Storage reference
      final ref = _storage.ref().child('chat_images/${task.fileName}');

      // Add metadata
      final metadata = SettableMetadata(
        contentType: _getContentType(task.localFile.path),
        customMetadata: {
          'uploadedBy': _auth.currentUser!.uid,
          'uploadedAt': DateTime.now().toIso8601String(),
          'messageId': task.messageId,
          'chatId': task.chatId,
        },
      );

      // Start upload
      final uploadTask = ref.putFile(task.localFile, metadata);
      
      // Track progress
      final subscription = uploadTask.snapshotEvents.listen(
        (snapshot) {
          final progress = snapshot.bytesTransferred / snapshot.totalBytes;
          task.progress = progress;
          _uploadStatusController.add(task);
        },
        onError: (error) {
          _handleUploadError(task, error);
        },
      );

      _uploadSubscriptions[task.id] = subscription;

      // Wait for completion
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      // Update task as completed
      task.status = UploadStatus.completed;
      task.progress = 1.0;
      task.remoteUrl = downloadUrl;
      _uploadStatusController.add(task);

      // Update message in Firestore
      await _updateMessageWithRemoteUrl(task.chatId, task.messageId, downloadUrl);

      // Clean up
      _uploadSubscriptions[task.id]?.cancel();
      _uploadSubscriptions.remove(task.id);

    } catch (e) {
      _handleUploadError(task, e);
    } finally {
      _activeUploads--;
      _processUploadQueue(); // Process next in queue
    }
  }

  /// Handle upload errors with retry logic
  void _handleUploadError(UploadTask task, dynamic error) {
    task.error = error.toString();
    
    if (task.retryCount < maxRetries) {
      // Retry after delay
      task.retryCount++;
      task.status = UploadStatus.queued;
      _uploadStatusController.add(task);
      
      // Exponential backoff
      final delay = Duration(seconds: task.retryCount * 2);
      Timer(delay, () => _processUploadQueue());
    } else {
      // Max retries reached
      task.status = UploadStatus.failed;
      _uploadStatusController.add(task);
      
      // Update message status to failed
      _updateMessageStatus(task.chatId, task.messageId, MessageStatus.failed);
    }

    // Clean up subscription
    _uploadSubscriptions[task.id]?.cancel();
    _uploadSubscriptions.remove(task.id);
  }

  /// Update message with remote URL in Firestore
  Future<void> _updateMessageWithRemoteUrl(
    String chatId,
    String messageId,
    String remoteUrl,
  ) async {
    try {
      await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .update({
        'mediaUrl': remoteUrl,
        'status': MessageStatus.sent.name,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      // print('Error updating message with remote URL: $e');
    }
  }

  /// Update message status in Firestore
  Future<void> _updateMessageStatus(
    String chatId,
    String messageId,
    MessageStatus status,
  ) async {
    try {
      await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .update({
        'status': status.name,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      // print('Error updating message status: $e');
    }
  }

  /// Cancel upload
  void cancelUpload(String uploadId) {
    final task = _uploadTasks[uploadId];
    if (task != null) {
      task.status = UploadStatus.cancelled;
      _uploadStatusController.add(task);
      
      _uploadSubscriptions[uploadId]?.cancel();
      _uploadSubscriptions.remove(uploadId);
      _uploadTasks.remove(uploadId);
    }
  }

  /// Get upload task by ID
  UploadTask? getUploadTask(String uploadId) {
    return _uploadTasks[uploadId];
  }

  /// Get all upload tasks for a message
  List<UploadTask> getUploadTasksForMessage(String messageId) {
    return _uploadTasks.values
        .where((task) => task.messageId == messageId)
        .toList();
  }

  /// Clear completed uploads
  void clearCompletedUploads() {
    final completedIds = _uploadTasks.entries
        .where((entry) => entry.value.status == UploadStatus.completed)
        .map((entry) => entry.key)
        .toList();

    for (final id in completedIds) {
      _uploadTasks.remove(id);
    }
  }

  /// Ensure Firebase authentication
  Future<void> _ensureFirebaseAuth() async {
    if (_auth.currentUser == null) {
      final currentUserId = await SessionService.getUserId();
      if (currentUserId == null) {
        throw Exception('User not logged in');
      }
      
      // Try to sign in anonymously or with custom token
      // This depends on your authentication setup
    }
  }

  /// Get content type for file
  String _getContentType(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      default:
        return 'application/octet-stream';
    }
  }

  /// Dispose resources
  void dispose() {
    for (final subscription in _uploadSubscriptions.values) {
      subscription.cancel();
    }
    _uploadSubscriptions.clear();
    _uploadTasks.clear();
    _uploadStatusController.close();
  }
}
