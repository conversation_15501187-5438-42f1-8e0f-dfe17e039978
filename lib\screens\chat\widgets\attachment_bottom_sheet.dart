import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';

class AttachmentBottomSheet extends StatelessWidget {
  final VoidCallback onCameraTap;
  final VoidCallback onGalleryTap;
  final VoidCallback onDocumentTap;
  final VoidCallback onCatalogTap;
  final bool isSendingFromCamera;
  final bool isSendingFromGallery;

  const AttachmentBottomSheet({ 
    super.key,
    required this.onCameraTap,
    required this.onGalleryTap,
    required this.onDocumentTap,
    required this.onCatalogTap,
    this.isSendingFromCamera = false,
    this.isSendingFromGallery = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Enhanced handle bar
          Container(
            width: 48,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 16),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Enhanced header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Share',
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: const Color(0xFF005368),
                      letterSpacing: 0.3,
                    ),
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(
                      Icons.close_rounded,
                      color: Color(0xFF005368),
                      size: 20,
                    ),
                    padding: const EdgeInsets.all(8),
                    constraints: const BoxConstraints(
                      minWidth: 40,
                      minHeight: 40,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Enhanced attachment options grid
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                // First row with improved spacing
                Row(
                  children: [
                    Expanded(
                      child: _buildAttachmentOption(
                        icon: LucideIcons.camera,
                        label: 'Camera',
                        onTap: isSendingFromCamera ? null : onCameraTap,
                        isLoading: isSendingFromCamera,
                        color: const Color(0xFF2196F3),
                        gradient: const LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [Color(0xFF2196F3), Color(0xFF1976D2)],
                        ), 
                      ),
                    ), 
                    const SizedBox(width: 20),
                    Expanded(
                      child: _buildAttachmentOption(
                        icon: LucideIcons.image,
                        label: 'Gallery',
                        onTap: isSendingFromGallery ? null : onGalleryTap,
                        isLoading: isSendingFromGallery,
                        color: const Color(0xFF4CAF50),
                        gradient: const LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [Color(0xFF4CAF50), Color(0xFF388E3C)],
                        ),
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: _buildAttachmentOption(
                        icon: LucideIcons.fileText,
                        label: 'Document',
                        onTap: onDocumentTap,
                        color: const Color(0xFFFF9800),
                        gradient: const LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [Color(0xFFFF9800), Color(0xFFF57C00)],
                        ),
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: _buildAttachmentOption(
                        icon: LucideIcons.store,
                        label: 'Catalog',
                        onTap: onCatalogTap,
                        color: const Color(0xFF9C27B0),
                        gradient: const LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [Color(0xFF9C27B0), Color(0xFF7B1FA2)],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Enhanced bottom padding for safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom + 24),
        ],
      ),
    );
  }

  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required VoidCallback? onTap,
    required Color color,
    required Gradient gradient,
    bool isLoading = false,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Column(
            children: [
              // Enhanced icon container with gradient
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  gradient: gradient,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: color.withOpacity(0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child:
                    isLoading
                        ? const Center(
                          child: SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          ),
                        )
                        : Icon(icon, color: Colors.white, size: 28),
              ),
              const SizedBox(height: 12),

              // Enhanced label
              Text(
                label,
                style: GoogleFonts.poppins(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF005368),
                  letterSpacing: 0.3,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
