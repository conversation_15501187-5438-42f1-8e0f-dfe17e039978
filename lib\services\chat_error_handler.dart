import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/services/background_upload_service.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

/// Comprehensive error handling service for chat functionality
class ChatErrorHandler {
  static final ChatErrorHandler _instance = ChatErrorHandler._internal();
  factory ChatErrorHandler() => _instance;
  ChatErrorHandler._internal();

  final Connectivity _connectivity = Connectivity();
  final Map<String, int> _retryAttempts = {};
  final Map<String, Timer> _retryTimers = {};

  static const int maxRetryAttempts = 3;
  static const Duration baseRetryDelay = Duration(seconds: 2);

  /// Handle upload errors with retry logic
  Future<void> handleUploadError({
    required String messageId,
    required String chatId,
    required File localFile,
    required String error,
    required VoidCallback onRetrySuccess,
    required Function(String) onRetryFailed,
    BuildContext? context,
  }) async {
    final retryCount = _retryAttempts[messageId] ?? 0;

    if (retryCount >= maxRetryAttempts) {
      // Max retries reached
      _showMaxRetriesError(context, error);
      onRetryFailed('Max retry attempts reached: $error');
      _retryAttempts.remove(messageId);
      return;
    }

    // Check connectivity
    final connectivityResults = await _connectivity.checkConnectivity();
    if (connectivityResults.contains(ConnectivityResult.none) ||
        connectivityResults.isEmpty) {
      _showOfflineError(context);
      _scheduleRetryWhenOnline(
        messageId,
        chatId,
        localFile,
        onRetrySuccess,
        onRetryFailed,
      );
      return;
    }

    // Determine retry strategy based on error type
    final retryStrategy = _determineRetryStrategy(error);

    switch (retryStrategy) {
      case RetryStrategy.immediate:
        await _retryImmediately(
          messageId,
          chatId,
          localFile,
          onRetrySuccess,
          onRetryFailed,
        );
        break;
      case RetryStrategy.delayed:
        _scheduleDelayedRetry(
          messageId,
          chatId,
          localFile,
          onRetrySuccess,
          onRetryFailed,
          retryCount,
        );
        break;
      case RetryStrategy.exponentialBackoff:
        _scheduleExponentialBackoffRetry(
          messageId,
          chatId,
          localFile,
          onRetrySuccess,
          onRetryFailed,
          retryCount,
        );
        break;
      case RetryStrategy.none:
        _showPermanentError(context, error);
        onRetryFailed('Permanent error: $error');
        break;
    }
  }

  /// Manual retry triggered by user
  Future<void> manualRetry({
    required String messageId,
    required String chatId,
    required File localFile,
    required VoidCallback onRetrySuccess,
    required Function(String) onRetryFailed,
    BuildContext? context,
  }) async {
    // Reset retry count for manual retries
    _retryAttempts[messageId] = 0;

    // Cancel any existing retry timer
    _retryTimers[messageId]?.cancel();
    _retryTimers.remove(messageId);

    // Check connectivity first
    final connectivityResults = await _connectivity.checkConnectivity();
    if (connectivityResults.contains(ConnectivityResult.none) ||
        connectivityResults.isEmpty) {
      _showOfflineError(context);
      return;
    }

    // Show retry in progress
    if (context != null) {
      AppSnackbar.showInfo(context, 'Retrying upload...');
    }

    await _performRetry(
      messageId,
      chatId,
      localFile,
      onRetrySuccess,
      onRetryFailed,
    );
  }

  /// Determine retry strategy based on error type
  RetryStrategy _determineRetryStrategy(String error) {
    final errorLower = error.toLowerCase();

    if (errorLower.contains('network') ||
        errorLower.contains('timeout') ||
        errorLower.contains('connection')) {
      return RetryStrategy.exponentialBackoff;
    }

    if (errorLower.contains('server') ||
        errorLower.contains('internal') ||
        errorLower.contains('503') ||
        errorLower.contains('502')) {
      return RetryStrategy.delayed;
    }

    if (errorLower.contains('unauthorized') ||
        errorLower.contains('forbidden') ||
        errorLower.contains('invalid') ||
        errorLower.contains('malformed')) {
      return RetryStrategy.none;
    }

    // Default to exponential backoff for unknown errors
    return RetryStrategy.exponentialBackoff;
  }

  /// Retry immediately
  Future<void> _retryImmediately(
    String messageId,
    String chatId,
    File localFile,
    VoidCallback onRetrySuccess,
    Function(String) onRetryFailed,
  ) async {
    await _performRetry(
      messageId,
      chatId,
      localFile,
      onRetrySuccess,
      onRetryFailed,
    );
  }

  /// Schedule delayed retry
  void _scheduleDelayedRetry(
    String messageId,
    String chatId,
    File localFile,
    VoidCallback onRetrySuccess,
    Function(String) onRetryFailed,
    int retryCount,
  ) {
    final delay = baseRetryDelay * (retryCount + 1);

    _retryTimers[messageId] = Timer(delay, () async {
      await _performRetry(
        messageId,
        chatId,
        localFile,
        onRetrySuccess,
        onRetryFailed,
      );
    });
  }

  /// Schedule exponential backoff retry
  void _scheduleExponentialBackoffRetry(
    String messageId,
    String chatId,
    File localFile,
    VoidCallback onRetrySuccess,
    Function(String) onRetryFailed,
    int retryCount,
  ) {
    final delay = Duration(
      seconds: (baseRetryDelay.inSeconds * (1 << retryCount)).clamp(2, 60),
    );

    _retryTimers[messageId] = Timer(delay, () async {
      await _performRetry(
        messageId,
        chatId,
        localFile,
        onRetrySuccess,
        onRetryFailed,
      );
    });
  }

  /// Schedule retry when back online
  void _scheduleRetryWhenOnline(
    String messageId,
    String chatId,
    File localFile,
    VoidCallback onRetrySuccess,
    Function(String) onRetryFailed,
  ) {
    // Listen for connectivity changes
    _connectivity.onConnectivityChanged.listen((
      List<ConnectivityResult> results,
    ) {
      if (results.isNotEmpty && results.first != ConnectivityResult.none) {
        // Back online, retry
        _performRetry(
          messageId,
          chatId,
          localFile,
          onRetrySuccess,
          onRetryFailed,
        );
      }
    });
  }

  /// Perform the actual retry
  Future<void> _performRetry(
    String messageId,
    String chatId,
    File localFile,
    VoidCallback onRetrySuccess,
    Function(String) onRetryFailed,
  ) async {
    try {
      _retryAttempts[messageId] = (_retryAttempts[messageId] ?? 0) + 1;

      // Queue upload with background service
      final uploadService = BackgroundUploadService();
      await uploadService.queueUpload(
        messageId: messageId,
        chatId: chatId,
        localFile: localFile,
      );

      onRetrySuccess();

      // Clean up retry tracking
      _retryAttempts.remove(messageId);
      _retryTimers[messageId]?.cancel();
      _retryTimers.remove(messageId);
    } catch (e) {
      await handleUploadError(
        messageId: messageId,
        chatId: chatId,
        localFile: localFile,
        error: e.toString(),
        onRetrySuccess: onRetrySuccess,
        onRetryFailed: onRetryFailed,
      );
    }
  }

  /// Show offline error message
  void _showOfflineError(BuildContext? context) {
    if (context != null) {
      AppSnackbar.showError(
        context,
        'No internet connection. Upload will retry when back online.',
      );
    }
  }

  /// Show max retries error
  void _showMaxRetriesError(BuildContext? context, String error) {
    if (context != null) {
      AppSnackbar.showError(
        context,
        'Upload failed after multiple attempts. Please try again later.',
      );
    }
  }

  /// Show permanent error
  void _showPermanentError(BuildContext? context, String error) {
    if (context != null) {
      AppSnackbar.showError(
        context,
        'Upload failed: ${_getUserFriendlyError(error)}',
      );
    }
  }

  /// Convert technical errors to user-friendly messages
  String _getUserFriendlyError(String error) {
    final errorLower = error.toLowerCase();

    if (errorLower.contains('network') || errorLower.contains('connection')) {
      return 'Network connection problem';
    }
    if (errorLower.contains('timeout')) {
      return 'Upload timed out';
    }
    if (errorLower.contains('unauthorized')) {
      return 'Authentication required';
    }
    if (errorLower.contains('forbidden')) {
      return 'Permission denied';
    }
    if (errorLower.contains('file too large') || errorLower.contains('size')) {
      return 'File is too large';
    }
    if (errorLower.contains('invalid format') ||
        errorLower.contains('format')) {
      return 'Invalid file format';
    }
    if (errorLower.contains('server')) {
      return 'Server temporarily unavailable';
    }

    return 'Upload failed';
  }

  /// Cancel retry for a message
  void cancelRetry(String messageId) {
    _retryTimers[messageId]?.cancel();
    _retryTimers.remove(messageId);
    _retryAttempts.remove(messageId);
  }

  /// Get retry count for a message
  int getRetryCount(String messageId) {
    return _retryAttempts[messageId] ?? 0;
  }

  /// Check if message is being retried
  bool isRetrying(String messageId) {
    return _retryTimers.containsKey(messageId);
  }

  /// Dispose resources
  void dispose() {
    for (final timer in _retryTimers.values) {
      timer.cancel();
    }
    _retryTimers.clear();
    _retryAttempts.clear();
  }
}

/// Retry strategies for different error types
enum RetryStrategy {
  immediate, // Retry immediately
  delayed, // Retry after a fixed delay
  exponentialBackoff, // Retry with exponential backoff
  none, // Don't retry (permanent error)
}

/// Error types for better categorization
enum ChatErrorType {
  network,
  authentication,
  permission,
  fileSize,
  fileFormat,
  server,
  unknown,
}

/// Extension for Message to add error handling helpers
extension MessageErrorHandling on Message {
  /// Check if message can be retried
  bool get canRetry => status == MessageStatus.failed && hasLocalFile;

  /// Get user-friendly error message
  String get userFriendlyError {
    if (uploadError == null) return 'Upload failed';
    return ChatErrorHandler()._getUserFriendlyError(uploadError!);
  }

  /// Check if error is due to network issues
  bool get isNetworkError {
    if (uploadError == null) return false;
    final errorLower = uploadError!.toLowerCase();
    return errorLower.contains('network') ||
        errorLower.contains('connection') ||
        errorLower.contains('timeout');
  }
}
