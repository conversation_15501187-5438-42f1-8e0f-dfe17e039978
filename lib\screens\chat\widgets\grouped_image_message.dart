import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/utils/image_utils.dart';
import 'package:mr_garments_mobile/utils/enhanced_image_utils.dart';
import 'package:mr_garments_mobile/screens/chat/widgets/image_viewer_screen.dart';
import 'package:mr_garments_mobile/screens/chat/widgets/enhanced_image_viewer_screen.dart';
import 'package:mr_garments_mobile/screens/chat/widgets/image_forward_dialog.dart';
import 'package:mr_garments_mobile/models/message.dart';

class GroupedImageMessage extends StatelessWidget {
  final List<String> imageUrls;
  final List<Message> imageMessages; // The actual message objects
  final String senderName;
  final String timestamp;
  final bool isMe;
  final String chatId;
  final VoidCallback? onLongPress;

  const GroupedImageMessage({
    super.key,
    required this.imageUrls,
    required this.imageMessages,
    required this.senderName,
    required this.timestamp,
    required this.isMe,
    required this.chatId,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    // Determine the layout based on number of images
    if (imageUrls.isEmpty) return const SizedBox();
    if (imageUrls.length == 1) {
      return _buildSingleImage(context);
    }
    return _buildGridView(context);
  }

  Widget _buildSingleImage(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    // WhatsApp-like sizing: smaller max width and more reasonable constraints
    final maxWidth = screenWidth * 0.5; // Reduced from 0.6 to 0.5
    const minWidth = 140.0; // Minimum width to prevent too small images
    const maxHeight = 220.0; // Reduced from 250 to 200
    const minHeight = 140.0; // Minimum height

    return GestureDetector(
      onTap: () => _openImageViewer(context, 0),
      onLongPress: () => _showImageOptions(context),
      child: Container(
        constraints: BoxConstraints(
          minWidth: minWidth,
          maxWidth: maxWidth,
          minHeight: minHeight,
          maxHeight: maxHeight,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: AspectRatio(
            // Use a more WhatsApp-like aspect ratio for single images
            aspectRatio: 4 / 3, // 4:3 aspect ratio, similar to WhatsApp
            child: _buildImageWithStatus(
              imageUrls[0],
              imageMessages[0],
              width: maxWidth,
              height: maxHeight,
              fit: BoxFit.cover,
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGridView(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final displayCount = imageUrls.length > 4 ? 4 : imageUrls.length;
    final remainingCount = imageUrls.length - displayCount;

    return GestureDetector(
      onTap: () => _openImageViewer(context, 0),
      onLongPress: () => _showImageOptions(context),
      child: Container(
        width: screenWidth * 0.55, // Reduced from 0.6 to 0.55
        height: 220, // Reduced from 250 to 220
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
        child: LayoutBuilder(
          builder: (context, constraints) {
            if (imageUrls.length == 2) {
              return _buildTwoImageLayout(constraints);
            } else if (imageUrls.length == 3) {
              return _buildThreeImageLayout(constraints);
            } else {
              return _buildFourPlusImageLayout(constraints, remainingCount);
            }
          },
        ),
      ),
    );
  }

  Widget _buildTwoImageLayout(BoxConstraints constraints) {
    return Row(
      children: [
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(right: 1),
            child: ClipRRect(
              borderRadius: const BorderRadius.horizontal(
                left: Radius.circular(12),
              ),
              child: _buildImageWithStatus(
                imageUrls[0],
                imageMessages[0],
                width: constraints.maxWidth / 2,
                height: constraints.maxHeight,
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(left: 1),
            child: ClipRRect(
              borderRadius: const BorderRadius.horizontal(
                right: Radius.circular(12),
              ),
              child: _buildImageWithStatus(
                imageUrls[1],
                imageMessages[1],
                width: constraints.maxWidth / 2,
                height: constraints.maxHeight,
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildThreeImageLayout(BoxConstraints constraints) {
    return Row(
      children: [
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(right: 1),
            child: ClipRRect(
              borderRadius: const BorderRadius.horizontal(
                left: Radius.circular(12),
              ),
              child: ImageUtils.buildChatImage(
                imageUrls[0],
                width: constraints.maxWidth / 2,
                height: constraints.maxHeight,
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(left: 1),
            child: Column(
              children: [
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 1),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(12),
                      ),
                      child: ImageUtils.buildChatImage(
                        imageUrls[1],
                        width: constraints.maxWidth / 2,
                        height: constraints.maxHeight / 2,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(top: 1),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(
                        bottomRight: Radius.circular(12),
                      ),
                      child: ImageUtils.buildChatImage(
                        imageUrls[2],
                        width: constraints.maxWidth / 2,
                        height: constraints.maxHeight / 2,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFourPlusImageLayout(
    BoxConstraints constraints,
    int remainingCount,
  ) {
    return Column(
      children: [
        Expanded(
          child: Row(
            children: [
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(right: 1, bottom: 1),
                  child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                    ),
                    child: ImageUtils.buildChatImage(
                      imageUrls[0],
                      width: constraints.maxWidth / 2,
                      height: constraints.maxHeight / 2,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(left: 1, bottom: 1),
                  child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(12),
                    ),
                    child: ImageUtils.buildChatImage(
                      imageUrls[1],
                      width: constraints.maxWidth / 2,
                      height: constraints.maxHeight / 2,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: Row(
            children: [
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(right: 1, top: 1),
                  child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                    ),
                    child: ImageUtils.buildChatImage(
                      imageUrls[2],
                      width: constraints.maxWidth / 2,
                      height: constraints.maxHeight / 2,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(left: 1, top: 1),
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      ClipRRect(
                        borderRadius: const BorderRadius.only(
                          bottomRight: Radius.circular(12),
                        ),
                        child: ImageUtils.buildChatImage(
                          imageUrls[3],
                          width: constraints.maxWidth / 2,
                          height: constraints.maxHeight / 2,
                          fit: BoxFit.cover,
                        ),
                      ),
                      if (remainingCount > 0)
                        ClipRRect(
                          borderRadius: const BorderRadius.only(
                            bottomRight: Radius.circular(12),
                          ),
                          child: Container(
                            color: Colors.black.withValues(alpha: 0.5),
                            child: Center(
                              child: Text(
                                '+$remainingCount',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _openImageViewer(BuildContext context, int initialIndex) {
    // Use enhanced image viewer if chatId is available for full chat navigation
    if (chatId.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => EnhancedImageViewerScreen(
                chatId: chatId,
                initialImageUrl: imageUrls[initialIndex],
                senderName: senderName,
                timestamp: timestamp,
              ),
        ),
      );
    } else {
      // Fallback to original viewer for grouped images only
      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => ImageViewerScreen(
                imageUrls: imageUrls,
                imageMessages: imageMessages,
                initialIndex: 0,
                senderName: senderName,
                timestamp: timestamp,
                chatId: chatId,
                onImagesDeleted: (deletedIndices) {
                  // Handle image deletion - this will be handled by the parent chat screen
                  // The parent should refresh the message list
                },
              ),
        ),
      );
    }
  }

  void _showImageOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle bar
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                // Forward option
                ListTile(
                  leading: const Icon(Icons.forward, color: Color(0xFF005368)),
                  title: Text(
                    'Forward Images',
                    style: TextStyle(fontSize: 16, color: Colors.black87),
                  ),
                  subtitle: Text(
                    '${imageUrls.length} image${imageUrls.length > 1 ? 's' : ''}',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _showForwardDialog(context);
                  },
                ),

                // View all images option
                ListTile(
                  leading: const Icon(
                    Icons.photo_library,
                    color: Color(0xFF005368),
                  ),
                  title: const Text(
                    'View All Images',
                    style: TextStyle(fontSize: 16, color: Colors.black87),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _openImageViewer(context, 0);
                  },
                ),

                // Message options (if callback provided)
                if (onLongPress != null)
                  ListTile(
                    leading: const Icon(
                      Icons.more_horiz,
                      color: Color(0xFF005368),
                    ),
                    title: const Text(
                      'Message Options',
                      style: TextStyle(fontSize: 16, color: Colors.black87),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      onLongPress?.call();
                    },
                  ),

                // Bottom padding for safe area
                SizedBox(height: MediaQuery.of(context).padding.bottom + 20),
              ],
            ),
          ),
    );
  }

  void _showForwardDialog(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ImageForwardScreen(
              imageUrls: imageUrls,
              imageMessages: imageMessages,
              fromChatId: chatId,
            ),
      ),
    );
  }

  /// Build image with status indicator for pending/failed messages
  Widget _buildImageWithStatus(
    String imageUrl,
    Message message, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
  }) {
    // Check if this is a local file (pending message)
    final isLocalFile = message.metadata?['isLocalFile'] == true;

    if (isLocalFile) {
      // Use enhanced image utils for local files with status
      return EnhancedImageUtils.buildImageWithStatus(
        imageUrl,
        status: message.status,
        width: width,
        height: height,
        fit: fit,
        borderRadius: borderRadius,
        isLocalFile: true,
      );
    } else {
      // Use regular image utils for network images
      return ImageUtils.buildChatImage(
        imageUrl,
        width: width ?? 200,
        height: height ?? 200,
        fit: fit,
        borderRadius: borderRadius ?? BorderRadius.zero,
      );
    }
  }
}
